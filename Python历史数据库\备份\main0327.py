# coding=utf-8

import openpyxl
from datetime import datetime
import re
from openpyxl.styles import PatternFill
from openpyxl.styles import Font
from openpyxl.utils import get_column_letter
import sys
from datetime import datetime

def compare_dates(date1: str, date2: str) -> int:
    date_format = "%Y-%m-%d"
    dt1 = datetime.strptime(date1, date_format)
    dt2 = datetime.strptime(date2, date_format)
    
    if dt1 > dt2:
        return 1
    elif dt1 < dt2:
        return -1
    else:
        return 0
    
def load_ICD_map(file_path):
    workbook = openpyxl.load_workbook(file_path)
    sheet = workbook.active
    data_dict = {}
    for row in sheet.iter_rows(values_only=True):
        key = row[0]
        value = row[2]
        if value == 'C__._':
            value = ''
        if len(str(key)) > 0 and key != None and len(str(value)) > 0 and value != None:
            data_dict[key] = value

    return data_dict


# 定义ICD03形态学编码表字典
def load_ICD03_map(file_path):
    workbook = openpyxl.load_workbook(file_path)
    sheet = workbook.active
    print(sheet)
    
# 获取ICD03表格中当前行的第二个元素到最后一个元素，存储在 key_lst 列表中
# 将 k 去除首尾空格后作为键，将当前行的第一个元素去除首尾空格后转换为字符串类型作为值，存储到 data_dict 字典中
    data_dict = {}
    for row in sheet.iter_rows(values_only=True):
        if len(row) > 1:
            key_lst = row[1:]
            for k in key_lst:
                if k is not None and isinstance(k, str):
                    if k != '':
                        data_dict[k.strip()] = str(row[0]).strip()

    return data_dict

def is_date(string):
    date_pattern = r'^\d{4}-\d{2}-\d{2}$'
    return bool(re.match(date_pattern, string)) and datetime.strptime(string, '%Y-%m-%d').strftime('%Y-%m-%d') == string

def is_datetime_string(s):
    try:
        datetime.strptime(s, '%Y-%m-%d %H:%M:%S')
        return True
    except ValueError:
        return False

def convert_datetime_to_date(datetime_str):
    datetime_obj = datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')
    date_str = datetime_obj.strftime('%Y-%m-%d')
    return date_str
    
def is_id_card(s):
    if len(s) != 18:
        return False
    pattern = r'^\d{6}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}(\d|X)$'
    return bool(re.match(pattern, s))

def get_age(id_number):
    birth_year = int(id_number[6:10])
    current_year = datetime.now().year
    return current_year - birth_year

BLX_dict = {
            'III级': 3,'Ⅲ级': 3,'低分化': 3,'分化差': 3,'异型性': 3,
            'II级': 2,'Ⅱ级': 2,'中分化': 2,'已中等分化': 2,
            'I级': 1, 'Ⅰ级': 1,'高分化': 1,'已分化': 1,'NOS': 1,'分化好': 1,
            'IV级': 4,'Ⅳ级': 4,'未分化': 4,'间变性': 4,'去分化': 4,'多形性': 4,
            'T-细胞': 5,
            'B-细胞': 6,'前-B': 6,'B-前体细胞': 6,
            '无标记淋巴细胞': 7,'非T-非B': 7,
            'NK（自然杀伤）细胞': 8,'NK(自然杀伤)细胞': 8,
            '等级或分化程度未确定': 9,'未指出或不适用的细胞类型未确定': 9,'未指出或不适用的': 9,'未指出': 9,'不适用': 9,'不详': 9,'未确定': 9,'不适用的': 9,'未检测': 9
            }

def blx_lvl_get(s):
    for key in BLX_dict.keys():
        if key in s:
            return BLX_dict[key]
    return 9

if __name__ == "__main__" :
    if len(sys.argv) < 2:
        print('Usage: [in file] [out file]')
        exit(0)
    #
    ICD_dict = load_ICD_map('ICD.xlsx')
    ICD03_dict = load_ICD03_map('ICD03.xlsx')

    # 打开源文件和目标文件
    source_file = sys.argv[1]
    target_file = sys.argv[2]
    source_wb = openpyxl.load_workbook(source_file)
    target_wb = openpyxl.Workbook()

    # 获取源文件中的第一个工作表
    source_ws = source_wb.active
    maxRow = source_ws.max_row
    maxCol = source_ws.max_column
    if maxRow <= 1:
        print("row not enough")
        exit(0)

    # 创建目标文件中的第一个工作表
    target_ws = target_wb.active
    pattern = PatternFill(fill_type='lightUp', start_color="FFFF00", end_color='FFFF00')

    # copy first row
    first_row = []
    for col in range(1, source_ws.max_column + 1):
        # 将源单元格的值复制到目标单元格
        target_ws.cell(row=1, column=col).value = source_ws.cell(row=1, column=col).value
        first_row.append(source_ws.cell(row=1, column=col).value)
    #print(first_row)

    # 遍历源工作表中的所有行和列
    bFirstLine = True
    #err_str_lst = []
    rowIdx = 1
    available_num = 0
    for row in source_ws.iter_rows():
        # copy first row
        if bFirstLine:
            bFirstLine = False   
            target_ws.cell(row=rowIdx, column=maxCol + 1, value='情况汇总') 
            rowIdx = rowIdx + 1  
        #convert other row
        else:
            err_str = ''
            i = 0     
            ICD_val=''
            ICD_coordinate = ''
            ICDO3_val = ''   
            ICD03_coordinate = ''
            BLX_lvl= ''
            XTX_code = ''
            zhenduan_date=''
            baogao_date=''
            shouka_date=''
            siwang_date=''
            linchuang_T=True
            linchuang_N=True
            linchuang_M=True
            bingli_T=True
            bingli_N=True
            bingli_M=True
            TNM_fenqi = ''
            TNM_fenqi_coordinate=''
            zhenduanyiju_num=0
            zhenduanyiju_coordinate = ''
            huji_type = ''
            huji_type_coordinate = ''
            huji_addr = ''
            home_addr = ''
            be_sz_time = ''
            finished = False

            for cell in row:
                if cell.value == None:
                    cell.value = ''

                # 将源单元格的值复制到目标单元格
                if first_row[i] == '报告类型':
                    if cell.value == '新发':
                        target_ws[cell.coordinate].value = 1
                    elif cell.value == '订正':
                        target_ws[cell.coordinate].value = 2
                    elif cell.value == '死亡':
                        target_ws[cell.coordinate].value = 3
                    elif len(str(cell.value).strip()) == 0:
                        finished = True
                        break                       
                    else:   
                        target_ws[cell.coordinate].value = cell.value
                        target_ws[cell.coordinate].fill = pattern
                        err_str = err_str + "报告类型错误;"

                elif first_row[i] == '户籍类型':
                    available_num = available_num + 1
                    
                    if cell.value == '常住户籍' or cell.value == '常住':
                        target_ws[cell.coordinate].value = 1
                    elif cell.value == '常住非户籍':
                        target_ws[cell.coordinate].value = 2
                    elif cell.value == '流动':
                        target_ws[cell.coordinate].value = 3
                    else:   
                        target_ws[cell.coordinate].value = cell.value
                        target_ws[cell.coordinate].fill = pattern
                        err_str = err_str + "户籍类型错误;"

                    #add 20231118
                    huji_type = target_ws[cell.coordinate].value
                    huji_type_coordinate = cell.coordinate

                elif first_row[i] == 'ICD编码':
                    ICD_val = str(cell.value).strip()[:5]
                    ICD_coordinate = cell.coordinate
                    
                    if len(ICD_val):
                        #mirror
                        ICDO3_val = ICD_dict.get(str(ICD_val),'')

                        if ICDO3_val == '':
                            if ICD_val.endswith('.x') or ICD_val.endswith('.X'):
                                ICD_val = ICD_val[:-2]

                            target_ws[cell.coordinate].value = ICD_val
                        else:
                            #ICD_val = ICDO3_val
                            target_ws[cell.coordinate].value = ICD_val
                    else:
                        target_ws[cell.coordinate].value = cell.value
                        target_ws[cell.coordinate].fill = pattern
                        err_str = err_str + "ICD编码错误;"  

                elif first_row[i] == 'ICDO3':
                    ICD03_coordinate = cell.coordinate

                elif first_row[i] == '姓名':
                    target_ws[cell.coordinate].value = cell.value
                    if len(str(cell.value)) == 0 or cell.value == None:
                        target_ws[cell.coordinate].fill = pattern
                        err_str = err_str + "姓名缺失;"

                elif first_row[i] == '性别':
                    if cell.value == '男':
                        target_ws[cell.coordinate].value = '男'
                    elif cell.value == '女':
                        target_ws[cell.coordinate].value = '女'
                    else:
                        target_ws[cell.coordinate].value = cell.value
                        target_ws[cell.coordinate].fill = pattern
                        err_str = err_str + "性别错误;"

                elif first_row[i] == '出生日期':
                    target_ws[cell.coordinate].value = cell.value
                    if not is_date(str(cell.value)):
                        target_ws[cell.coordinate].fill = pattern
                        err_str = err_str + "出生日期错误;"

                elif first_row[i] == '身份证号码':
                    target_ws[cell.coordinate].value = cell.value
                    if not is_id_card(str(cell.value)):
                        target_ws[cell.coordinate].fill = pattern
                        err_str = err_str + "身份证号码错误;"
                    elif get_age(str(cell.value)) < 25:
                        target_ws[cell.coordinate].fill = pattern

                elif first_row[i] == '职业编号' or first_row[i] == '职业':
                    if cell.value == '国家公务员':
                        target_ws[cell.coordinate].value = 'A'
                    elif cell.value == '专业技术人员':
                        target_ws[cell.coordinate].value = 'B'
                    elif cell.value == '职员':
                        target_ws[cell.coordinate].value = 'C'
                    elif cell.value == '农民':
                        target_ws[cell.coordinate].value = 'E'   
                    elif cell.value == '工人':
                        target_ws[cell.coordinate].value = 'F'
                    elif cell.value == '军人':
                        target_ws[cell.coordinate].value = 'G'  
                    elif cell.value == '不便分类的其他从业人员':
                        target_ws[cell.coordinate].value = 'H'    
                    elif cell.value == '企业管理人员':
                        target_ws[cell.coordinate].value = 'J'     
                    elif cell.value == '学生':
                        target_ws[cell.coordinate].value = 'K'    
                    elif cell.value == '自由职业者':
                        target_ws[cell.coordinate].value = 'L'  
                    elif cell.value == '个体经营者':
                        target_ws[cell.coordinate].value = 'M'   
                    elif cell.value == '无业人员':
                        target_ws[cell.coordinate].value = 'N'   
                    elif cell.value == '退(离)休人员' or cell.value == '退（离）休人员':
                        target_ws[cell.coordinate].value = 'O'                                                                                                                                                                              
                    else:   
                        target_ws[cell.coordinate].value = cell.value 
                        target_ws[cell.coordinate].fill = pattern  
                        err_str = err_str + "职业编号错误;"  

                elif first_row[i] == '学历代码' or first_row[i] == '学历':
                    if cell.value == '研究生及以上':
                        target_ws[cell.coordinate].value = 6
                    elif cell.value == '大学本科':
                        target_ws[cell.coordinate].value = 5
                    elif cell.value == '高中':
                        target_ws[cell.coordinate].value = 4
                    elif cell.value == '初中':
                        target_ws[cell.coordinate].value = 3  
                    elif cell.value == '小学':
                        target_ws[cell.coordinate].value = 2
                    elif cell.value == '其他':
                        target_ws[cell.coordinate].value = 1   
                    elif cell.value == '中等职业技术':
                        target_ws[cell.coordinate].value = 7   
                    elif cell.value == '大学专科':
                        target_ws[cell.coordinate].value = 8   
                    else:   
                        target_ws[cell.coordinate].value = cell.value   
                        target_ws[cell.coordinate].fill = pattern  
                        err_str = err_str + "学历代码错误;"

                      
                elif first_row[i] == '籍贯':
                    #add 20231120
                    if '陕西' in str(cell.value):
                        target_ws[cell.coordinate].value = '陕西'
                    elif '甘肃' in str(cell.value):
                        target_ws[cell.coordinate].value = '甘肃'
                    elif '河北' in str(cell.value):
                        target_ws[cell.coordinate].value = '河北' 
                    elif '山西' in str(cell.value):
                        target_ws[cell.coordinate].value = '山西'
                    elif '辽宁' in str(cell.value):
                        target_ws[cell.coordinate].value = '辽宁'
                    elif '吉林' in str(cell.value):
                        target_ws[cell.coordinate].value = '吉林'
                    elif '黑龙江' in str(cell.value):
                        target_ws[cell.coordinate].value = '黑龙江'
                    elif '江苏' in str(cell.value):
                        target_ws[cell.coordinate].value = '江苏'
                    elif '浙江' in str(cell.value):
                        target_ws[cell.coordinate].value = '浙江'
                    elif '安徽' in str(cell.value):
                        target_ws[cell.coordinate].value = '安徽'
                    elif '福建' in str(cell.value):
                        target_ws[cell.coordinate].value = '福建'
                    elif '江西' in str(cell.value):
                        target_ws[cell.coordinate].value = '江西'    
                    elif '山东' in str(cell.value):
                        target_ws[cell.coordinate].value = '山东'
                    elif '河南' in str(cell.value):
                        target_ws[cell.coordinate].value = '河南'
                    elif '广东' in str(cell.value):
                        target_ws[cell.coordinate].value = '广东'
                    elif '湖北' in str(cell.value):
                        target_ws[cell.coordinate].value = '湖北'    
                    elif '湖南' in str(cell.value):
                        target_ws[cell.coordinate].value = '湖南'
                    elif '海南' in str(cell.value):
                        target_ws[cell.coordinate].value = '海南'
                    elif '四川' in str(cell.value):
                        target_ws[cell.coordinate].value = '四川'
                    elif '贵州' in str(cell.value):   
                        target_ws[cell.coordinate].value = '贵州'
                    elif '云南' in str(cell.value):
                        target_ws[cell.coordinate].value = '云南'
                    elif '福建' in str(cell.value):
                        target_ws[cell.coordinate].value = '福建'
                    elif '青海' in str(cell.value):
                        target_ws[cell.coordinate].value = '青海'
                    elif '台湾' in str(cell.value):
                        target_ws[cell.coordinate].value = '台湾'
                    elif '北京' in str(cell.value):
                        target_ws[cell.coordinate].value = '北京'
                    elif '上海' in str(cell.value):
                        target_ws[cell.coordinate].value = '上海'
                    elif '天津' in str(cell.value):
                        target_ws[cell.coordinate].value = '天津'
                    elif '重庆' in str(cell.value):
                        target_ws[cell.coordinate].value = '重庆'
                    elif '香港' in str(cell.value):
                        target_ws[cell.coordinate].value = '香港特别行政区'  
                    elif '澳门' in str(cell.value):
                        target_ws[cell.coordinate].value = '澳门特别行政区'  
                    elif '内蒙古' in str(cell.value):
                        target_ws[cell.coordinate].value = '内蒙古'  
                    elif '广西' in str(cell.value):
                        target_ws[cell.coordinate].value = '广西'  
                    elif '西藏' in str(cell.value):
                        target_ws[cell.coordinate].value = '西藏'  
                    elif '宁夏' in str(cell.value):
                        target_ws[cell.coordinate].value = '宁夏'  
                    elif '新疆' in str(cell.value):
                        target_ws[cell.coordinate].value = '新疆'  
                    elif len(str(cell.value)) == 0 or cell.value == None:
                        target_ws[cell.coordinate].fill = pattern
                        err_str = err_str + "籍贯缺失;"                        
                    else:
                        target_ws[cell.coordinate].value = cell.value


                elif first_row[i] == '婚姻状况':
                    if cell.value == '未婚':
                        target_ws[cell.coordinate].value = "01"
                    elif cell.value == '已婚':
                        target_ws[cell.coordinate].value = "02"                      
                    elif cell.value == '未知':
                        target_ws[cell.coordinate].value = "03" 
                    elif cell.value == '丧偶':
                        target_ws[cell.coordinate].value = "04"
                    elif cell.value == '离婚':
                        target_ws[cell.coordinate].value = "05"
                    elif cell.value == '初婚':
                        target_ws[cell.coordinate].value = "06" 
                    elif cell.value == '再婚':
                        target_ws[cell.coordinate].value = "07"                                                        
                    elif cell.value == '复婚':
                        target_ws[cell.coordinate].value = "08" 
                    else:   
                        target_ws[cell.coordinate].value = cell.value 
                        target_ws[cell.coordinate].fill = pattern
                        err_str = err_str + "婚姻状况错误;"    
                        

                elif first_row[i] == '在深居住(年)':
                    if cell.value is None or cell.value == '':
                        target_ws[cell.coordinate].value = 99
                    else:
                        try:
                            be_sz_time = float(cell.value)
                        except ValueError:
                            be_sz_time = 99
                        finally:
                            target_ws[cell.coordinate].value = be_sz_time
    
                                 
                elif first_row[i] == '户籍地址(省)':
                    target_ws[cell.coordinate].value = str(cell.value).replace('省', '')
                    if len(str(cell.value)) == 0 or cell.value == None:
                        target_ws[cell.coordinate].fill = pattern 
                        err_str = err_str + "户籍地址(省)缺失;"     
                                    
                                           
                elif first_row[i] == '户籍地址(市)':
                    target_ws[cell.coordinate].value = str(cell.value).replace('市', '')
                    if len(str(cell.value)) == 0 or cell.value == None:
                        target_ws[cell.coordinate].fill = pattern 
                        err_str = err_str + "户籍地址(市)缺失;"
                    elif cell.value == "省直辖县级行政区划":
                        target_ws[cell.coordinate].value = "省直辖县级行政单位"        
                        

                    huji_addr =  str(target_ws[cell.coordinate].value)                
                elif first_row[i] == '户籍地址(县)':
                    if str(cell.value)[-1] == '县' or str(cell.value)[-1] == '区' or str(cell.value)[-1] == '市':
                        target_ws[cell.coordinate].value = str(cell.value)[:-1]
                    else:
                        target_ws[cell.coordinate].value = cell.value
                    if len(str(cell.value)) == 0 or cell.value == None:
                        target_ws[cell.coordinate].fill = pattern    
                        err_str = err_str + "户籍地址(县)缺失;" 
                          
                                                       
                elif first_row[i] == '户籍地址(乡)':
                    target_ws[cell.coordinate].value = cell.value
                    if len(str(cell.value)) == 0 or cell.value == None:
                        target_ws[cell.coordinate].fill = pattern  
                        err_str = err_str + "户籍地址(乡)缺失;"                                           
                                                       
                                                            
                elif first_row[i] == '家庭地址(省)':
                    target_ws[cell.coordinate].value = str(cell.value).replace('省', '')
                    if len(str(cell.value)) == 0 or cell.value == None:
                        target_ws[cell.coordinate].fill = pattern 
                        err_str = err_str + "家庭地址(省)缺失;"     
                                   
                        
                elif first_row[i] == '家庭地址(市)':
                    target_ws[cell.coordinate].value = str(cell.value).replace('市', '')
                    if len(str(cell.value)) == 0 or cell.value == None:
                        target_ws[cell.coordinate].fill = pattern    
                        err_str = err_str + "家庭地址(市)缺失;"  
                    elif cell.value == "省直辖县级行政区划":
                        target_ws[cell.coordinate].value = "省直辖区县级行政单位"        
                        
                    home_addr =  str(target_ws[cell.coordinate].value)

                elif first_row[i] == '家庭地址(县)':
                    if str(cell.value)[-1] == '县' or str(cell.value)[-1] == '区' or str(cell.value)[-1] == '市':
                        target_ws[cell.coordinate].value = str(cell.value)[:-1]
                    else:
                        target_ws[cell.coordinate].value = cell.value
                    if len(str(cell.value)) == 0 or cell.value == None:
                        target_ws[cell.coordinate].fill = pattern  
                        err_str = err_str + "家庭地址(县)缺失;"          
                              
                elif first_row[i] == '家庭地址(乡)':
                    target_ws[cell.coordinate].value = cell.value
                    if len(str(cell.value)) == 0 or cell.value == None:
                        target_ws[cell.coordinate].fill = pattern 
                        err_str = err_str + "家庭地址(乡)缺失;"      
                        
                              
                elif first_row[i] == '家庭地址(村)':
                    target_ws[cell.coordinate].value = cell.value
                    if len(str(cell.value)) == 0 or cell.value == None:
                        err_str = err_str + "家庭地址(村)缺失;"  
                        
                        
                elif first_row[i] == '家庭地址(电话)':
                    target_ws[cell.coordinate].value = cell.value
                    if len(str(cell.value)) == 0 or cell.value == None:
                        target_ws[cell.coordinate].fill = pattern 
                        err_str = err_str + "家庭地址(电话)缺失;"  

                #用“肿瘤诊断名称”列来配，
                #若出现“恶性”或“癌”或“肉瘤”或“淋巴瘤”且不出现“原位” ICD10 第一位是C
                #若出现“原位” ICD10 第一位是D
                #若出现“宫颈上皮内”且出现“2级"或"Ⅱ”字样, ICD10是N87.1
                #若出现“宫颈上皮内”且出现“3级"或“III”或"Ⅱ-III”字样, ICD10 是D06.9
                
                elif first_row[i] == '肿瘤诊断名称':
                    target_ws[cell.coordinate].value = cell.value 
                    if len(str(cell.value)) == 0 or cell.value == None:
                        target_ws[cell.coordinate].fill = pattern 
                        err_str = err_str + "肿瘤诊断名称缺失;"  
                    else:
                        if (str(cell.value).find('恶性') != -1 or str(cell.value).find('癌') != -1 or str(cell.value).find('肉瘤') != -1 or str(cell.value).find('淋巴瘤') != -1) and str(cell.value).find('原位癌') == -1:
                            if len(ICD_val) > 0 and ICD_val[0] !='C':
                                target_ws[ICD_coordinate].fill = pattern 
                                err_str = err_str + "ICD编码校验不通过，应当C开头;" 
                        elif str(cell.value).find('原位') != -1:
                            if len(ICD_val) > 0 and ICD_val[0] !='D':
                                target_ws[ICD_coordinate].fill = pattern 
                                err_str = err_str + "ICD编码校验不通过，应当D开头;"  
                        elif  str(cell.value).find('宫颈上皮内') != -1 and (str(cell.value).find('3级') != -1 or str(cell.value).find('Ⅲ') != -1  or str(cell.value).find('III') != -1):
                            if len(ICD_val) > 0 and ICD_val !='D06.9':
                                target_ws[ICD_coordinate].fill = pattern 
                                err_str = err_str + "ICD编码校验不通过,应对为D06.9;" 
                        elif  str(cell.value).find('宫颈上皮内') != -1 and (str(cell.value).find('2级') != -1 or str(cell.value).find('Ⅱ') != -1  or str(cell.value).find('II') != -1):
                            if len(ICD_val) > 0 and ICD_val !='N87.1':
                                target_ws[ICD_coordinate].fill = pattern 
                                err_str = err_str + "ICD编码校验不通过,应对为N87.1;" 
                        else:
                            pass
                                
                elif first_row[i] == '肿瘤位置':
                    if cell.value == '左侧':
                        target_ws[cell.coordinate].value = 1
                    elif cell.value == '右侧':
                        target_ws[cell.coordinate].value = 2                      
                    elif cell.value == '双侧':
                        target_ws[cell.coordinate].value = 3  
                    elif cell.value == '不详':
                        target_ws[cell.coordinate].value = 4
                    else:   
                        target_ws[cell.coordinate].value = cell.value 
                        target_ws[cell.coordinate].fill = pattern  
                        err_str = err_str + "肿瘤位置错误;"  

                #7-原发肿瘤的组织学检查  显示6
                #6-转移灶的组织学检查 显示7
                #其余的则可以直接提取前面的数字
                #7-临床 显示7
                #6-临床 显示6     
                     
                elif first_row[i] == '诊断依据':
                    zhenduanyiju_coordinate = cell.coordinate
                    zhenduanyiju_num = str(cell.value).count('-')   
                    if zhenduanyiju_num == 1:
                        index = str(cell.value).find('-')
                        if str(cell.value).strip() == '7-原发肿瘤的组织学检查':
                            target_ws[cell.coordinate].value = '6'
                        elif str(cell.value).strip() == '6-转移灶的组织学检查':
                            target_ws[cell.coordinate].value = '7'
                        elif index != -1:
                            target_ws[cell.coordinate].value = str(cell.value)[:index]
                        else:   
                            target_ws[cell.coordinate].value = cell.value 
                            target_ws[cell.coordinate].fill = pattern  
                            err_str = err_str + "诊断依据错误;"  
                    elif zhenduanyiju_num >= 2:
                        if '0-' in str(cell.value):
                            target_ws[cell.coordinate].value = '0'
                        elif '7-' in str(cell.value) or '6-病理（原发）' in str(cell.value):
                            target_ws[cell.coordinate].value = '6'
                        elif '6-转移灶的组织学检查' in str(cell.value):
                            target_ws[cell.coordinate].value = '7'
                        elif '5-' in str(cell.value):
                            target_ws[cell.coordinate].value = '5'
                        elif '4-' in str(cell.value):
                            target_ws[cell.coordinate].value = '4'
                        elif '9-' in str(cell.value):
                            target_ws[cell.coordinate].value = '9'
                        elif '2-' in str(cell.value):
                            target_ws[cell.coordinate].value = '2'
                        elif '1-' in str(cell.value):
                            target_ws[cell.coordinate].value = '1'

                    else:
                        target_ws[cell.coordinate].value = cell.value 
                        target_ws[cell.coordinate].fill = pattern  
                        err_str = err_str + "诊断依据错误;"  
           
                #20231120       
                #诊断依据大于1个，那就是多个的意思，再看后面的“病理学类型”列，有内容就是7，如果内容是“未提供、详见病理、无”字段，就填写2-临床检查
                elif first_row[i] == '病理学类型':
                    #if zhenduanyiju_num >= 2:
                        #if len(str(cell.value)) > 0 and cell.value !=None:
                            #target_ws[zhenduanyiju_coordinate].value = '7'
                    
                    BLX_lvl = blx_lvl_get(str(cell.value))

                    XTX_code = ICD03_dict.get(str(cell.value).strip(),'')

                    if '不详' in str(cell.value).strip() or '未提供' in str(cell.value).strip()  or '未指出' in str(cell.value).strip()  or '未确定' in str(cell.value).strip() or '详见病理' in str(cell.value).strip()  or str(cell.value).strip() == '无' or  '未检测' in str(cell.value).strip() or '不适用' in str(cell.value).strip() or str(cell.value).strip() == '':
                        target_ws[cell.coordinate].fill = pattern 
                        target_ws[cell.coordinate].value = ""
                        err_str = err_str + "病理学类型异常;" 
                        #if zhenduanyiju_num >= 2:
                            #target_ws[zhenduanyiju_coordinate].value = '2'
                    else:
                        target_ws[cell.coordinate].value = cell.value

                elif first_row[i] == '多原发':
                    if cell.value == '是':
                        target_ws[cell.coordinate].value = 1
                    elif cell.value == '否':
                        target_ws[cell.coordinate].value = 0                     
                    else:   
                        target_ws[cell.coordinate].value = cell.value  
                        target_ws[cell.coordinate].fill = pattern 
                        err_str = err_str + "多原发错误;" 

                elif first_row[i] == 'TNM分期':
                    TNM_fenqi_coordinate = cell.coordinate
                    if cell.value == '是':
                        target_ws[cell.coordinate].value = 1
                        TNM_fenqi = 1
                    elif cell.value == '否':
                        target_ws[cell.coordinate].value = 0    
                        TNM_fenqi = 0
                    else:   
                        target_ws[cell.coordinate].value = cell.value 
                        target_ws[cell.coordinate].fill = pattern 
                        err_str = err_str + "TNM分期错误;"   
                                     
                elif first_row[i] == '临床T':
                    if cell.value == 'T0':
                        target_ws[cell.coordinate].value = 0
                    elif cell.value == 'T1':
                        target_ws[cell.coordinate].value = 1  
                    elif cell.value == 'T2':
                        target_ws[cell.coordinate].value = 2   
                    elif cell.value == 'T3':
                        target_ws[cell.coordinate].value = 3  
                    elif cell.value == 'T4':
                        target_ws[cell.coordinate].value = 4  
                    elif cell.value == 'X(未知)':
                        target_ws[cell.coordinate].value = 5  
                    elif cell.value == 'IS原位癌':
                        target_ws[cell.coordinate].value = 6                                          
                    else:   
                        target_ws[cell.coordinate].value = cell.value 
                        target_ws[cell.coordinate].fill = pattern 
                        linchuang_T = False
                        err_str = err_str + "临床T缺失;"    

                elif first_row[i] == '临床N':
                    if cell.value == 'N0':
                        target_ws[cell.coordinate].value = 0
                    elif cell.value == 'N1':
                        target_ws[cell.coordinate].value = 1  
                    elif cell.value == 'N2':
                        target_ws[cell.coordinate].value = 2   
                    elif cell.value == 'N3':
                        target_ws[cell.coordinate].value = 3  
                    elif cell.value == 'X(未知)':
                        target_ws[cell.coordinate].value = 5                                           
                    else:   
                        target_ws[cell.coordinate].value = cell.value  
                        target_ws[cell.coordinate].fill = pattern 
                        linchuang_N = False
                        err_str = err_str + "临床N缺失;"                      

                elif first_row[i] == '临床M':
                    if cell.value == 'M0(不存在)':
                        target_ws[cell.coordinate].value = 0
                    elif cell.value == 'M1(存在)':
                        target_ws[cell.coordinate].value = 1                                            
                    else:   
                        target_ws[cell.coordinate].value = cell.value   
                        target_ws[cell.coordinate].fill = pattern 
                        linchuang_M = False
                        err_str = err_str + "临床M缺失;"           
                    
                    
                elif first_row[i] == '临床分期':
                    if cell.value == '0期':
                        target_ws[cell.coordinate].value = 1
                    elif cell.value == 'Ⅰ期' or cell.value == 'I期':
                        target_ws[cell.coordinate].value = 2  
                    elif cell.value == 'Ⅱ期' or cell.value == 'II期':
                        target_ws[cell.coordinate].value = 3
                    elif cell.value == 'Ⅲ期' or cell.value == 'III期':
                        target_ws[cell.coordinate].value = 4 
                    elif cell.value == 'Ⅳ期' or cell.value == 'IV期':
                        target_ws[cell.coordinate].value = 5   
                    elif cell.value == '无法判定':
                        target_ws[cell.coordinate].value = 6   
                    elif cell.value == '':
                        target_ws[cell.coordinate].value = ''                                                                                                                         
                    else:   
                        target_ws[cell.coordinate].value = cell.value 
                        target_ws[cell.coordinate].fill = pattern 
                        err_str = err_str + "临床分期错误;"   


                elif first_row[i] == '诊断日期':
                    if is_date(str(cell.value)):
                         target_ws[cell.coordinate].value = cell.value
                         zhenduan_date = target_ws[cell.coordinate].value
                    elif is_datetime_string(str(cell.value)):
                        target_ws[cell.coordinate].value = convert_datetime_to_date(str(cell.value))
                        zhenduan_date = target_ws[cell.coordinate].value
                    elif str(cell.value) == '':
                        target_ws[cell.coordinate].value = ''                       
                    else:
                        target_ws[cell.coordinate].value = cell.value
                        target_ws[cell.coordinate].fill = pattern 
                        err_str = err_str + "诊断日期错误;"  

                elif first_row[i] == '已告知病人':
                    if cell.value == '是':
                        target_ws[cell.coordinate].value = 1
                    elif cell.value == '否':
                        target_ws[cell.coordinate].value = 2   
                    elif cell.value == '不详':
                        target_ws[cell.coordinate].value = 3                                        
                    else:   
                        target_ws[cell.coordinate].value = cell.value 
                        target_ws[cell.coordinate].fill = pattern
                        err_str = err_str + "已告知病人错误;"     

                elif first_row[i] == '死亡日期':
                    target_ws[cell.coordinate].value = cell.value
                    if len(str(cell.value)) > 0 and cell.value != None:
                        siwang_date =  target_ws[cell.coordinate].value
                        
                elif first_row[i] == '报告医生':
                    target_ws[cell.coordinate].value = cell.value
                    if len(str(cell.value)) == 0:
                        target_ws[cell.coordinate].fill = pattern  
                        err_str = err_str + "报告医生缺失;"  

                elif first_row[i] == '死亡原因':
                    if str(cell.value) != '因本病死亡':
                        pass 
                    elif len(ICD_val):
                        target_ws[cell.coordinate].value = ICD_val
                    else:
                        target_ws[cell.coordinate].value = cell.value

                elif first_row[i] == '报告日期':
                    if is_date(str(cell.value)):
                         target_ws[cell.coordinate].value = cell.value
                         baogao_date = target_ws[cell.coordinate].value
                    elif is_datetime_string(str(cell.value)):
                        target_ws[cell.coordinate].value = convert_datetime_to_date(str(cell.value))
                        baogao_date = target_ws[cell.coordinate].value
                    else:
                        target_ws[cell.coordinate].value = cell.value
                        target_ws[cell.coordinate].fill = pattern 
                        err_str = err_str + "报告日期错误;"  
                      
                elif first_row[i] == '报告单位':
                    target_ws[cell.coordinate].value = cell.value
                    if len(str(cell.value)) == 0 or cell.value == None:
                        target_ws[cell.coordinate].fill = pattern
                        err_str = err_str + "报告单位缺失;"     

                elif first_row[i] == '收卡人':
                    target_ws[cell.coordinate].value = cell.value
                    if len(str(cell.value)) == 0 or cell.value == None:
                        target_ws[cell.coordinate].fill = pattern  
                        err_str = err_str + "收卡人缺失;"   

                elif first_row[i] == '收卡日期':
                    if is_date(str(cell.value)):
                        target_ws[cell.coordinate].value = cell.value
                        shouka_date = target_ws[cell.coordinate].value
                    elif is_datetime_string(str(cell.value)):
                        target_ws[cell.coordinate].value = convert_datetime_to_date(str(cell.value))
                        shouka_date = target_ws[cell.coordinate].value
                    else:
                        target_ws[cell.coordinate].value = cell.value
                        target_ws[cell.coordinate].fill = pattern 
                        err_str = err_str + "收卡日期错误;"                         

                elif first_row[i] == '收卡单位':
                    target_ws[cell.coordinate].value = '南山区慢病院'                                  

                elif first_row[i] == '录入人(姓名)':
                    target_ws[cell.coordinate].value = cell.value
                    if len(str(cell.value)) == 0 or cell.value == None:
                        target_ws[cell.coordinate].fill = pattern  
                        err_str = err_str + "录入人(姓名)缺失;" 

                elif first_row[i] == '知情同意随访':
                    if cell.value == '是':
                        target_ws[cell.coordinate].value = 1
                    elif cell.value == '否':
                        target_ws[cell.coordinate].value = 0                                       
                    else:   
                        target_ws[cell.coordinate].value = cell.value  
                        err_str = err_str + "知情同意随访错误;"  

                elif first_row[i] == '民族':
                    target_ws[cell.coordinate].value = cell.value
                    if len(str(cell.value)) == 0 or cell.value == None:
                        target_ws[cell.coordinate].fill = pattern 
                        err_str = err_str + "民族缺失;"   

                elif first_row[i] == '病理T':
                    if cell.value == 'T0':
                        target_ws[cell.coordinate].value = 0
                    elif cell.value == 'T1':
                        target_ws[cell.coordinate].value = 1  
                    elif cell.value == 'T2':
                        target_ws[cell.coordinate].value = 2   
                    elif cell.value == 'T3':
                        target_ws[cell.coordinate].value = 3  
                    elif cell.value == 'T4':
                        target_ws[cell.coordinate].value = 4  
                    elif cell.value == 'X(未知)':
                        target_ws[cell.coordinate].value = 5  
                    elif cell.value == 'IS原位癌':
                        target_ws[cell.coordinate].value = 6                                          
                    else:   
                        target_ws[cell.coordinate].value = cell.value 
                        target_ws[cell.coordinate].fill = pattern 
                        bingli_T = False
                        err_str = err_str + "病理T缺失;"    

                elif first_row[i] == '病理N':
                    if cell.value == 'N0':
                        target_ws[cell.coordinate].value = 0
                    elif cell.value == 'N1':
                        target_ws[cell.coordinate].value = 1  
                    elif cell.value == 'N2':
                        target_ws[cell.coordinate].value = 2   
                    elif cell.value == 'N3':
                        target_ws[cell.coordinate].value = 3  
                    elif cell.value == 'X(未知)':
                        target_ws[cell.coordinate].value = 5                                           
                    else:   
                        target_ws[cell.coordinate].value = cell.value  
                        target_ws[cell.coordinate].fill = pattern 
                        bingli_N = False
                        err_str = err_str + "病理N缺失;"                      

                elif first_row[i] == '病理M':
                    if cell.value == 'M0(不存在)':
                        target_ws[cell.coordinate].value = 0
                    elif cell.value == 'M1(存在)':
                        target_ws[cell.coordinate].value = 1                                            
                    else:   
                        target_ws[cell.coordinate].value = cell.value   
                        target_ws[cell.coordinate].fill = pattern 
                        bingli_M = False
                        err_str = err_str + "病理M缺失;"                     

            
                elif first_row[i] == '紧急联系人1关系':
                    if cell.value == '本人或户主':
                        target_ws[cell.coordinate].value = 0
                    elif cell.value == '配偶':
                        target_ws[cell.coordinate].value = 1   
                    elif cell.value == '子':
                        target_ws[cell.coordinate].value = 2  
                    elif cell.value == '女':
                        target_ws[cell.coordinate].value = 3  
                    elif cell.value == '孙子、孙女,或外孙女、外孙女':
                        target_ws[cell.coordinate].value = 4  
                    elif cell.value == '父母':
                        target_ws[cell.coordinate].value = 5  
                    elif cell.value == '祖父母或外祖父母':
                        target_ws[cell.coordinate].value = 6  
                    elif cell.value == '兄、弟、姐、妹':
                        target_ws[cell.coordinate].value = 7   
                    elif cell.value == '其他':
                        target_ws[cell.coordinate].value = 8                                                                                                                                                                                      
                    else:   
                        target_ws[cell.coordinate].value = cell.value  
                        err_str = err_str + "紧急联系人1关系缺失;"                     


                elif first_row[i] == '紧急联系人2关系':
                    if cell.value == '本人或户主':
                        target_ws[cell.coordinate].value = 0
                    elif cell.value == '配偶':
                        target_ws[cell.coordinate].value = 1   
                    elif cell.value == '子':
                        target_ws[cell.coordinate].value = 2  
                    elif cell.value == '女':
                        target_ws[cell.coordinate].value = 3  
                    elif cell.value == '孙子、孙女,或外孙女、外孙女':
                        target_ws[cell.coordinate].value = 4  
                    elif cell.value == '父母':
                        target_ws[cell.coordinate].value = 5  
                    elif cell.value == '祖父母或外祖父母':
                        target_ws[cell.coordinate].value = 6  
                    elif cell.value == '兄、弟、姐、妹':
                        target_ws[cell.coordinate].value = 7   
                    elif cell.value == '其他':
                        target_ws[cell.coordinate].value = 8                                                                                                                                                                                      
                    else:   
                        target_ws[cell.coordinate].value = cell.value  
                        err_str = err_str + "紧急联系人2关系缺失;"                     
                else:
                    target_ws[cell.coordinate].value = cell.value

                i = i + 1
   
            #最后处理
            if finished:
                break

            #ICD03 = C34.9-8140/3-9
            target_ws[ICD03_coordinate].value = ICDO3_val + '-' + XTX_code + '-' + str(BLX_lvl)
            print(target_ws[ICD03_coordinate].value)

            if ICDO3_val == '':
                target_ws[ICD03_coordinate].value = ''
                err_str = err_str + "ICD03缺失;"
            if XTX_code == '':
                target_ws[ICD03_coordinate].value = ''
                err_str = err_str + "形态学编码缺失;"
            #if BLX_lvl == 9:
                #target_ws[ICD03_coordinate].value = ''
                #err_str = err_str + "组织学等级缺失;"

            if target_ws[ICD03_coordinate].value == '':
                target_ws[ICD03_coordinate].fill = pattern


            #最后处理日期比较
            if len(zhenduan_date) and len(baogao_date) and compare_dates(zhenduan_date,baogao_date) > 0:
                err_str = err_str + "诊断日期大于报告日期;" 
            if len(baogao_date) and len(shouka_date) and compare_dates(baogao_date,shouka_date) > 0:
                err_str = err_str + "报告日期大于收卡日期;" 
            if len(zhenduan_date) and len(siwang_date) and compare_dates(zhenduan_date,siwang_date) > 0:
                err_str = err_str + "诊断日期大于死亡日期;" 
            if len(siwang_date) and len(baogao_date) and compare_dates(siwang_date,baogao_date) > 0:
                err_str = err_str + "死亡日期大于报告日期;" 
                         
            if (linchuang_T or linchuang_N or linchuang_M) or (bingli_T or bingli_N or bingli_M):
                if TNM_fenqi == 0:
                    err_str = err_str + "TNM分期应为是;" 
                    target_ws[TNM_fenqi_coordinate].fill = pattern 
            else:
                if TNM_fenqi == 1:
                    err_str = err_str + "TNM分期应为否;" 
                    target_ws[TNM_fenqi_coordinate].fill = pattern 

            #对比户籍类型检查规则,常住1,常住非户籍2,流动3
            if huji_type == 1:
                if not (home_addr == '深圳' and huji_addr == '深圳'):
                    err_str = err_str + "户籍类型错误;" 
                    target_ws[huji_type_coordinate].fill = pattern
            elif huji_type == 2:
                if not (home_addr == '深圳' and huji_addr != '深圳' and (float(be_sz_time) if be_sz_time else 0.5) >= 0.5):
                    err_str = err_str + "户籍类型错误;" 
                    target_ws[huji_type_coordinate].fill = pattern
            elif huji_type == 3:
                if home_addr == '深圳':
                    if not (home_addr == '深圳' and huji_addr != '深圳' and (float(be_sz_time) if be_sz_time else 0.5) < 0.5):
                        err_str = err_str + "户籍类型错误;" 
                        target_ws[huji_type_coordinate].fill = pattern


            #写入error
            if len(err_str):
                target_ws.cell(row=rowIdx, column=maxCol + 1, value=err_str)
                target_ws.cell(row=rowIdx, column=maxCol + 1, value=err_str).fill = pattern

            rowIdx = rowIdx + 1
        

    #加宽
    last_column_letter = get_column_letter(target_ws.max_column)
    target_ws.column_dimensions[last_column_letter].width = 50

    # 设置第一行的字体加粗
    for cell in target_wb.active[1]:
        cell.font = Font(bold=True)

    # 保存目标文件
    try:
        target_wb.save(target_file)
        print('-------')
        print('Success')
        print('有效'+str(available_num)+'行')
        print('处理完毕，输出到文件:'+target_file)
    except IOError as e:
        if e.errno == 13:
            print(f"保存文件错误，请先关闭文件"+target_file)
        else:
            print(f"发生错误，错误信息为：{e.strerror}")
        

